# ABOUTME: Real-time team status channel for broadcasting employee work activity updates
# ABOUTME: Provides company-scoped streaming for managers to see live team status changes

class TeamStatusChannel < ApplicationCable::Channel
  def subscribed
    # Company-scoped streaming for managers to see team status updates
    if current_tenant && can_view_team_status?
      stream_for current_tenant
      logger.info "[TeamStatusChannel] User #{current_user.email} subscribed to team status for company #{current_tenant.name}"
    else
      reject
      logger.warn "[TeamStatusChannel] User #{current_user.email} rejected - insufficient permissions or no tenant"
    end
  end

  def unsubscribed
    logger.info "[TeamStatusChannel] User #{current_user.email} unsubscribed from team status"
  end

  private

  def can_view_team_status?
    # Check if user has manager role (Owner, Supervisor, Administrator)
    # This matches the permissions in MainboxPolicy
    current_user.has_role?(:owner, current_tenant) ||
    current_user.has_role?(:supervisor, current_tenant) ||
    current_user.has_role?(:admin, current_tenant)
  end
end