<template>
  <div class="time-tracking" v-cloak>
    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <Clock :size="24" class="animate-spin text-blue-500" />
    </div>

    <template v-else>
      <!-- Special event display -->
      <div v-if="todayEvent" class="card bg-white rounded-lg p-4 mb-4 max-w-[500px]">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <CalendarClock :size="20" class="mr-2 text-blue-500" />
            <span class="font-medium">{{ todayEvent.t_event_type }}</span>
          </div>
          <div>
            <LocalizedLink :to="'/daily_logs/report'" class="text-link-action blue" :use-anchor="true">
              {{ $t('monthly_report', 'Měsíční výkaz') }}
            </LocalizedLink>
          </div>
        </div>
      </div>

      <!-- Main tracking component -->
      <div v-else class="card">
        <div class="bg-white rounded-lg">
          <div v-if="!isWorking" class="p-5">
            <div class="flex justify-between items-center mb-4">
              <div class="flex items-center">
                <Clock :size="24" class="mr-2 text-gray-500" />
                <div>
                  <h3 class="font-medium text-lg">{{ $t('work_hours', 'Pracovní doba') }}</h3>
                  <p class="text-sm text-gray-500">{{ $t('not_working', 'Nepracujete') }}</p>
                </div>
              </div>
              <div>
                <LocalizedLink :to="'/daily_logs/report'" class="text-link-action blue" :use-anchor="true">
                  {{ $t('monthly_report', 'Měsíční výkaz') }}
                </LocalizedLink>
              </div>
            </div>

            <!-- Previous work info -->
            <div class="grid grid-cols-3 gap-4 mb-4">
              <div class="bg-gray-50 p-3 rounded-md flex flex-col items-center">
                <div class="flex items-center mb-1">
                  <Flag :size="18" class="text-gray-500 mr-1" />
                  <span class="text-sm text-gray-500">{{ $t('start', 'Start') }}</span>
                </div>
                <span class="font-medium">
                  {{ lastDailyLog?.start_time ? formatTime(lastDailyLog.start_time) : '-' }}
                </span>
              </div>

              <div class="bg-gray-50 p-3 rounded-md flex flex-col items-center">
                <div class="flex items-center mb-1">
                  <CalendarClock :size="18" class="text-gray-500 mr-1" />
                  <span class="text-sm text-gray-500">{{ $t('end', 'Konec') }}</span>
                </div>
                <span class="font-medium">
                  {{ !isWorking && lastDailyLog?.end_time ? formatTime(lastDailyLog.end_time) : '-' }}
                </span>
              </div>

              <div class="bg-gray-50 p-3 rounded-md flex flex-col items-center">
                <div class="flex items-center mb-1">
                  <SquareActivity :size="18" class="text-gray-500 mr-1" />
                  <span class="text-sm text-gray-500">{{ $t('duration', 'Trvání') }}</span>
                </div>
                <span class="font-medium">
                  {{ lastDailyLog?.duration ? formatDuration(lastDailyLog.duration) : '-' }}
                </span>
              </div>
            </div>

            <button @click="startWork" class="w-full bg-green-500 text-white py-3 px-4 rounded-md flex items-center justify-center">
              <PlayCircle :size="20" class="mr-2" />
              <span class="font-medium">{{ $t('start_work', 'Začít práci') }}</span>
            </button>
          </div>

          <!-- Working state - compact display -->
          <div v-else class="p-4">
            <!-- Time display -->
            <div class="flex justify-between items-center mb-3">
              <div class="flex items-center">
                <Clock :size="20" class="mr-2 text-green-500" />
                <div>
                  <h3 class="font-medium">{{ formattedElapsedTime }}</h3>
                  <p class="text-sm text-green-500">{{ $t('work_in_progress', 'V práci') }}</p>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <!-- Break button -->
                <template v-if="!settings.auto_break && !todayBreak">
                  <button @click="startBreak" class="flex items-center bg-gray-100 text-gray-700 py-2 px-3 rounded-md text-sm hover:bg-gray-200 transition-colors">
                    <UtensilsCrossed :size="16" class="mr-1" />
                    <span class="hidden sm:inline">{{ $t('break', 'Přestávka') }}</span>
                  </button>
                </template>

                <!-- End break button -->
                <template v-if="!settings.auto_break && todayBreak && !todayBreak.end_time">
                  <button @click="endBreak" class="flex items-center bg-blue-500 text-white py-2 px-3 rounded-md text-sm hover:bg-blue-600 transition-colors">
                    <span>{{ $t('end_break', 'Ukončit přestávku') }}</span>
                  </button>
                </template>

                <!-- Activity button -->
                <!-- <button @click="openWorkActivityModal" class="flex items-center bg-blue-100 text-blue-700 py-2 px-3 rounded-md text-sm hover:bg-blue-200 transition-colors">
                  <Plus :size="16" class="mr-1" />
                  <span class="inline">{{ $t('activity', 'Aktivita') }}</span>
                </button> -->

                <!-- End work button -->
                <button @click="endWork" class="flex items-center bg-red-500 text-white py-2 px-3 rounded-md text-sm hover:bg-red-600 transition-colors">
                  <StopCircle :size="16" class="mr-1" />
                  <span>{{ $t('end', 'Konec') }}</span>
                </button>
              </div>
            </div>

            <!-- Current work activity banner -->
            <!-- <div v-if="currentWorkActivity"
              class="p-3 bg-green-50 rounded-md border border-green-200 cursor-pointer"
              @click="openWorkActivityModal"
              >
              <div class="flex items-start justify-between">
                <div class="flex items-start space-x-2">
                  <LandPlot :size="18" class="text-blue-600 mt-0.5 flex-shrink-0" />
                  <div class="min-w-0 flex-1">
                    <p class="text-sm font-medium text-blue-900 break-words">{{ currentWorkActivity.work.title }}</p>
                    <p class="text-xs text-blue-700 mt-0.5">{{ formatWorkActivityType(currentWorkActivity.activity_type) }}</p>
                    <p v-if="currentWorkActivity.work.location" class="text-xs text-blue-600 mt-1 flex items-center">
                      <MapPin :size="12" class="mr-1 flex-shrink-0" />
                      <span class="break-words">{{ currentWorkActivity.work.location }}</span>
                    </p>
                  </div>
                </div>
                <div>
                  <RefreshCcw :size="16" class="text-blue-600" />
                </div>
              </div>
            </div> -->
          </div>
        </div>
      </div>


      <!-- Unified Activity Modal -->
      <div v-if="showWorkActivityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-lg w-full">
          <h3 class="text-lg font-medium mb-4">{{ $t('select_activity', 'Vybrat aktivitu') }}</h3>

          <div v-if="loadingWorks" class="flex justify-center py-8">
            <Clock :size="24" class="animate-spin text-gray-500" />
          </div>

          <div v-else class="space-y-2 max-h-96 overflow-y-auto">
            <!-- Current work activity at the top if exists -->
            <div
              v-if="currentWorkActivity"
              class="p-3 border-2 border-blue-400 bg-blue-50 rounded-md"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="font-medium text-blue-900">{{ currentWorkActivity.work.title }}</div>
                  <div class="text-sm text-blue-700 mt-1">
                    {{ formatTime(currentWorkActivity.start_time) }} - {{ formatWorkActivityType(currentWorkActivity.activity_type) }}
                  </div>
                  <div v-if="currentWorkActivity.work.location" class="text-sm text-blue-600 flex items-center mt-1">
                    <MapPin :size="14" class="mr-1" />
                    {{ currentWorkActivity.work.location }}
                  </div>
                </div>
                <button
                  @click="openEndWorkDialog"
                  class="btn btn-small btn-danger"
                >
                  {{ $t('end', 'Ukončit') }}
                </button>
              </div>
            </div>

            <!-- Available works -->
            <button
              v-for="work in assignedWorks.filter(w => !currentWorkActivity || w.id !== currentWorkActivity.work_id)"
              :key="work.id"
              @click="quickStartWork(work)"
              class="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors group"
            >
              <div class="flex justify-between items-start">
                <div class="flex-1">
                  <div class="font-medium">{{ work.title }}</div>
                  <div v-if="work.location" class="text-sm text-gray-500 flex items-center mt-1">
                    <MapPin :size="14" class="mr-1" />
                    {{ work.location }}
                  </div>
                </div>
                <div class="text-sm text-gray-500">
                  {{ $t('works.activities.switch', 'přepnout') }}
                </div>
              </div>
            </button>

            <!-- Other activity input at the bottom -->
            <div
              v-if="currentOtherActivity"
              class="p-3 border-2 border-green-400 bg-green-50 rounded-md"
            >
              <div class="flex items-center justify-between">
                <div class="flex-1">
                  <div class="font-medium text-green-900">{{ currentOtherActivity.description }}</div>
                  <div class="text-sm text-green-700 mt-1">
                    {{ formatTime(currentOtherActivity.start_time) }}
                  </div>
                </div>
                <button
                  @click="endOtherActivity"
                  class="btn btn-small btn-danger"
                >
                  {{ $t('end', 'Ukončit') }}
                </button>
              </div>
            </div>

            <div v-else class="p-3 border border-gray-200 rounded-md bg-gray-50">
              <input
                v-model="otherActivityDescription"
                ref="otherActivityInput"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                :placeholder="$t('other_activity_placeholder', 'Jiná aktivita...')"
                @keyup.enter="saveOtherActivity"
                @focus="handleOtherActivityFocus"
              />
            </div>
          </div>

          <div class="mt-4 flex justify-end">
            <button
              @click="closeWorkActivityModal"
              class="btn btn-outline"
            >
              {{ $t('close', 'Zavřít') }}
            </button>
          </div>
        </div>
      </div>

      <!-- End Work Dialog -->
      <div v-if="showEndWorkDialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md w-full">
          <h3 class="text-lg font-medium mb-4">{{ $t('end_work_activity', 'Ukončit pracovní aktivitu') }}</h3>

          <div class="mb-4">
            <p class="text-sm text-gray-600 mb-2">{{ currentWorkActivity?.work.title }}</p>
            <textarea
              v-model="endWorkNotes"
              ref="endWorkNotesInput"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              :placeholder="$t('optional_notes_placeholder', 'Volitelné poznámky k provedené práci...')"
              rows="3"
            ></textarea>
          </div>

          <div class="flex justify-end gap-2">
            <button
              @click="cancelEndWork"
              class="btn btn-outline"
            >
              {{ $t('cancel', 'Zrušit') }}
            </button>
            <button
              @click="confirmEndWork"
              class="btn btn-danger"
            >
              {{ $t('end_work', 'Ukončit práci') }}
            </button>
          </div>
        </div>
      </div>

    </template>
  </div>
</template>

<script>
import axios from 'axios';
import { Clock, PlayCircle, PauseCircle, ChevronDown, ChevronUp, ChevronLeft, ChevronRight,
        UtensilsCrossed, CalendarClock, Flag, SquareActivity, FileText, Settings, Info, StopCircle, LandPlot, Plus, MapPin, RefreshCcw } from 'lucide-vue-next';
import { sendFlashMessage } from '@/utils/flashMessage';
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'TimeTracking',
  components: {
    Clock,
    PlayCircle,
    PauseCircle,
    StopCircle,
    ChevronDown,
    ChevronUp,
    ChevronLeft,
    ChevronRight,
    UtensilsCrossed,
    CalendarClock,
    Flag,
    SquareActivity,
    FileText,
    Settings,
    Info,
    LandPlot,
    Plus,
    MapPin,
    RefreshCcw,
    LocalizedLink
  },
  
  data() {
    return {
      isLoading: true,
      currentDailyLog: null,
      lastDailyLog: null, 
      currentActivity: null,
      isWorking: false,
      currentTime: '',
      timeInterval: null,
      todayBreak: null,
      todayEvent: null,
      settings: {
        auto_break: false,
        break_duration: 30
      },
      elapsedSeconds: 0,
      elapsedUpdateInterval: null,
      // Work activity properties
      showWorkActivityModal: false,
      assignedWorks: [],
      loadingWorks: false,
      currentWorkActivity: null,
      showOtherActivity: false,
      otherActivityDescription: '',
      showEndWorkDialog: false,
      endWorkNotes: '',
      currentOtherActivity: null
    };
  },

  methods: {
    formatTime(time) {
      return new Date(time).toLocaleTimeString('cs-CZ', {
        hour: '2-digit', 
        minute: '2-digit'
      });
    },
    
    formatDuration(duration) {
      if (!duration) return null;
      const hours = Math.floor(duration / 3600);
      const minutes = Math.floor((duration % 3600) / 60);
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    },

    updateCurrentTime() {
      this.currentTime = new Date().toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    startElapsedTimer() {
      this.stopElapsedTimer();
      this.updateElapsedTime();
      this.elapsedUpdateInterval = setInterval(() => {
        this.updateElapsedTime();
      }, 1000);
    },
    
    stopElapsedTimer() {
      if (this.elapsedUpdateInterval) {
        clearInterval(this.elapsedUpdateInterval);
        this.elapsedUpdateInterval = null;
      }
    },
    
    updateElapsedTime() {
      if (!this.lastDailyLog?.start_time) return;
      
      const startTime = new Date(this.lastDailyLog.start_time);
      const now = new Date();
      this.elapsedSeconds = Math.floor((now - startTime) / 1000);
    },

    async startWork() {
      try {
        const createResponse = await axios.post('/daily_logs');
        const { daily_log, notifications } = createResponse.data;

        this.currentDailyLog = daily_log; 
        this.lastDailyLog = daily_log;  
        this.isWorking = true;
        
        this.startElapsedTimer();

        // Emit event for other components
        document.dispatchEvent(new CustomEvent('daily-log-started', {
          detail: { daily_log: daily_log }
        }));

        if (notifications?.length) {
          notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        }
      } catch (error) {
        this.handleError(error, this.$t('start_work_error', 'Nepodařilo se zahájit práci'));
      }
    },

    async endWork() {
      if (!this.currentDailyLog?.id) { 
        console.error('No daily log ID available');
        return;
      }

      try {
        const endResponse = await axios.put(`/daily_logs/${this.currentDailyLog.id}`, {
          daily_log: { end_time: new Date().toISOString() }
        });
        
        this.isWorking = false;
        this.currentDailyLog = null;
        this.currentWorkActivity = null; // Clear current work activity
        this.currentOtherActivity = null; // Clear current other activity

        this.stopElapsedTimer();

        // Emit event for other components
        document.dispatchEvent(new CustomEvent('daily-log-ended', {
          detail: { daily_log: this.lastDailyLog }
        }));

        if (this.lastDailyLog) {
          this.lastDailyLog = {
            ...this.lastDailyLog,
            end_time: endResponse.data.end_time,
            duration_in_text: endResponse.data.duration_in_text
          };
        }

        await this.checkCurrentStatus();
        
        if (endResponse.data.notifications?.length) {
          endResponse.data.notifications.forEach(notification => {
            sendFlashMessage(this.$t(notification.message, notification.message), notification.type);
          });
        }

      } catch (error) {
        this.handleError(error, this.$t('end_work_error', 'Nepodařilo se ukončit práci'));
      }
    },





    async checkCurrentStatus() {
      try {
        const response = await axios.get('/daily_logs/current_status');
        const { last_log, today_break, settings, activities, today_event } = response.data;

        this.todayBreak = today_break;
        this.settings = settings;
        this.lastDailyLog = last_log; 
        this.todayEvent = today_event;
        
        if (last_log && !last_log.end_time) {
          this.currentDailyLog = last_log;
          this.isWorking = true;
          this.startElapsedTimer();
          // Also fetch current work activity if working
          await this.fetchCurrentWorkActivity();
        } else {
          this.currentWorkActivity = null;
          this.currentOtherActivity = null;
        }

        // Check for current other activity (non-work activity without end time)
        this.currentOtherActivity = activities?.find(activity =>
          !activity.end_time &&
          !activity.work_id &&
          (activity.activity_type === 'regular' || !activity.activity_type)
        ) || null;
      } catch (error) {
        console.error('Error checking status:', error);
      } finally {
        this.isLoading = false; 
      }
    },


    handleError(error, defaultMessage) {
      const errorMessage = error.response?.data?.errors?.join('\n') || defaultMessage;
      sendFlashMessage(errorMessage, 'alert');
    },

    async startBreak() {
      try {
        const response = await axios.post('/breaks', {
          break: {
            daily_log_id: this.currentDailyLog.id,
          }
        });
        this.todayBreak = response.data;
        sendFlashMessage(this.$t('break_started', 'Přestávka zahájena'), 'info');
      } catch (error) {
        this.handleError(error, this.$t('start_break_error', 'Nepodařilo se zahájit přestávku'));
      }
    },
    
    async endBreak() {
      try {
        const response = await axios.put(`/breaks/${this.todayBreak.id}`, {
          break: {
            end_time: new Date().toISOString()
          }
        });
        this.todayBreak = response.data;
        sendFlashMessage(this.$t('break_ended', 'Přestávka ukončena'), 'info');
      } catch (error) {
        this.handleError(error, this.$t('end_break_error', 'Nepodařilo se ukončit přestávku'));
      }
    },
    
    async fetchTodayBreak() {
      try {
        const response = await axios.get('/breaks/today');
        this.todayBreak = response.data;
      } catch (error) {
        console.error('Error fetching today\'s break:', error);
      }
    },




    
    formatWorkActivityType(type) {
      const typeTranslations = {
        travel_to_work: this.$t('travel', 'Cesta'),
        work_at_location: this.$t('working', 'V práci'),
        work_remote: this.$t('working', 'Práce'),
        other: this.$t('other', 'Jiné')
      };
      return typeTranslations[type] || type;
    },

    async handleWorkActivityChange() {
      // Refresh the current status when work activity changes
      await this.checkCurrentStatus();
    },

    // Work activity methods
    async openWorkActivityModal() {
      this.showWorkActivityModal = true;
      this.showOtherActivity = false;
      this.otherActivityDescription = '';
      await this.fetchAssignedWorks();
      await this.fetchCurrentWorkActivity();
    },

    closeWorkActivityModal() {
      this.showWorkActivityModal = false;
      this.showOtherActivity = false;
      this.otherActivityDescription = '';
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    },

    async fetchAssignedWorks() {
      this.loadingWorks = true;
      try {
        const response = await axios.get('/api/v1/works/assigned', {
          headers: { 'Accept': 'application/json' }
        });
        this.assignedWorks = Array.isArray(response.data) ? response.data : [];
      } catch (error) {
        console.error('Error fetching assigned works:', error);
        this.assignedWorks = [];
      } finally {
        this.loadingWorks = false;
      }
    },

    async fetchCurrentWorkActivity() {
      try {
        const response = await axios.get('/api/v1/daily_activities/current_work_activity', {
          headers: { 'Accept': 'application/json' }
        });
        this.currentWorkActivity = response.data.activity;
      } catch (error) {
        console.error('Error fetching current work activity:', error);
      }
    },

    async quickStartWork(work) {
      if (this.currentWorkActivity && work.id === this.currentWorkActivity.work_id) {
        return;
      }

      if (this.currentWorkActivity) {
        await this.endWorkActivity(false);
      }

      try {
        const response = await axios.post('/api/v1/daily_activities/start_work_activity', {
          work_id: work.id,
          activity_type: 'work_at_location',
          daily_log_id: this.currentDailyLog?.id
        });

        this.currentWorkActivity = response.data.activity;
        sendFlashMessage(response.data.message || this.$t('works.activities.work_activity_started', 'Pracovní aktivita byla zahájena'), 'success');
        this.closeWorkActivityModal();

        await this.fetchCurrentWorkActivity();
      } catch (error) {
        this.handleError(error, this.$t('works.activities.start_error', 'Chyba při zahájení práce'));
      }
    },

    async endWorkActivity(showMessage = true, notes = '') {
      if (!this.currentWorkActivity) return;

      try {
        const response = await axios.patch(`/api/v1/daily_activities/${this.currentWorkActivity.id}/end_work_activity`, {
          notes: notes
        });

        if (showMessage) {
          sendFlashMessage(response.data.message || this.$t('works.activities.work_activity_ended', 'Pracovní aktivita byla ukončena'), 'success');
        }
        this.currentWorkActivity = null;
        if (showMessage) {
          this.closeWorkActivityModal();
        }

      } catch (error) {
        this.handleError(error, this.$t('works.activities.end_error', 'Chyba při ukončení práce'));
      }
    },

    async saveOtherActivity() {
      if (!this.currentDailyLog) {
        sendFlashMessage(this.$t('start_work_first', 'Nejprve začněte práci'), 'alert');
        return;
      }

      if (!this.otherActivityDescription.trim()) {
        return;
      }

      if (this.currentWorkActivity) {
        await this.endWorkActivity(false);
      }

      try {
        const response = await axios.post('/api/v1/daily_activities', {
          daily_activity: {
            description: this.otherActivityDescription,
            daily_log_id: this.currentDailyLog.id,
            start_time: new Date().toISOString(),
            activity_type: 'regular'
          }
        });

        this.currentActivity = response.data;
        this.currentOtherActivity = response.data;
        this.otherActivityDescription = '';
        this.closeWorkActivityModal();
      } catch (error) {
        this.handleError(error, this.$t('save_activity_error', 'Nepodařilo se uložit aktivitu'));
      }
    },

    async endOtherActivity() {
      if (!this.currentOtherActivity) return;

      try {
        await axios.put(`/api/v1/daily_activities/${this.currentOtherActivity.id}`, {
          daily_activity: {
            end_time: new Date().toISOString()
          }
        });

        this.currentOtherActivity = null;
        sendFlashMessage(this.$t('other_activity_ended', 'Jiná aktivita byla ukončena'), 'success');
      } catch (error) {
        this.handleError(error, this.$t('end_other_activity_error', 'Nepodařilo se ukončit aktivitu'));
      }
    },

    handleOtherActivityFocus() {
      // Clear the input when focusing if it's a placeholder
      if (this.currentWorkActivity) {
        // Will end current work when they type and save
      }
    },

    openEndWorkDialog() {
      this.showEndWorkDialog = true;
      this.$nextTick(() => {
        this.$refs.endWorkNotesInput?.focus();
      });
    },

    cancelEndWork() {
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    },

    async confirmEndWork() {
      await this.endWorkActivity(true, this.endWorkNotes);
      this.showEndWorkDialog = false;
      this.endWorkNotes = '';
    }
  },

  
  mounted() {
    this.checkCurrentStatus();
    this.fetchAssignedWorks(); // Fetch assigned works on mount
    this.updateCurrentTime();
    this.timeInterval = setInterval(() => {
      this.updateCurrentTime();
    }, 1000);

    // Listen for work activity changes from other components
    document.addEventListener('work-activity-started', this.handleWorkActivityChange);
    document.addEventListener('work-activity-ended', this.handleWorkActivityChange);
  },

  beforeDestroy() {
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
    }
    this.stopElapsedTimer();

    // Clean up event listeners
    document.removeEventListener('work-activity-started', this.handleWorkActivityChange);
    document.removeEventListener('work-activity-ended', this.handleWorkActivityChange);
  },

  computed: {
    formattedElapsedTime() {
      const hours = Math.floor(this.elapsedSeconds / 3600);
      const minutes = Math.floor((this.elapsedSeconds % 3600) / 60);
      const seconds = this.elapsedSeconds % 60;

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    },
    hasAssignedWorks() {
      return this.assignedWorks.length > 0 || this.currentWorkActivity !== null;
    },
    availableWorks() {
      return this.assignedWorks;
    }
  },

  watch: {
    showOtherActivity(newVal) {
      if (newVal && this.showWorkActivityModal) {
        this.$nextTick(() => {
          this.$refs.otherActivityInput?.focus();
        });
      }
    }
  }
};
</script>

<style scoped>
[v-cloak] {
  display: none;
}

.animate-spin {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  100% {
    transform: rotate(360deg);
  }
}

/* Radio button group styles */
.btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0;
}

.btn-check {
  position: absolute;
  clip: rect(0,0,0,0);
  pointer-events: none;
}

.btn-outline-status {
  @apply px-3 py-2 text-sm border border-gray-300 bg-white text-gray-700;
  @apply hover:bg-gray-50 transition-colors cursor-pointer;
  flex: 1;
  text-align: center;
  white-space: nowrap;
}

.btn-outline-status:first-of-type {
  @apply rounded-l-md;
}

.btn-outline-status:last-of-type {
  @apply rounded-r-md;
}

.btn-outline-status:not(:first-of-type) {
  @apply -ml-px;
}

.btn-check:checked + .btn-outline-status {
  @apply bg-blue-500 text-white border-blue-500;
  @apply hover:bg-blue-600;
}

.btn-check:disabled + .btn-outline-status {
  @apply opacity-50 cursor-not-allowed;
}

/* Button styles */
.btn {
  @apply px-3 py-1.5 rounded-md text-sm font-medium transition-colors;
}

.btn-outline {
  @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50;
}

.btn-danger {
  @apply bg-red-500 text-white hover:bg-red-600;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .btn-group {
    gap: 0.5rem;
  }

  .btn-outline-status {
    @apply rounded-md;
    margin-left: 0 !important;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }
}
</style>